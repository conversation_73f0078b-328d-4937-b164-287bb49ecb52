<template>
  <div
    class="previous-income-deductions"
    style="display: flex; flex-direction: column; height: 100%"
  >
    <!-- 搜索区域 -->
    <el-form
      :inline="true"
      class="search"
      style="
        flex: 0 1 auto;
        margin-bottom: 20px;
        background: var(--o-primary-bg-color);
        padding: 20px 20px 0 20px;
        border-radius: 5px;
      "
      label-position="right"
      label-width="90px"
    >
      <div class="lite" style="display: flex; align-items: center">
        <div>
          <el-form-item label="税款所属期">
            <el-date-picker
              v-model="conditions.filters.taxPeriod"
              type="month"
              placeholder="选择月份"
              format="yyyy-MM"
              value-format="yyyy-MM"
              style="width: 280px"
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="姓名">
            <el-input
              v-model="conditions.filters.fullName"
              placeholder="请输入姓名"
              style="width: 280px"
            ></el-input>
          </el-form-item>
        </div>
        <div style="text-align: right; flex: 1">
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="default" @click="onReset">重置</el-button>
        </div>
      </div>
    </el-form>
    <div style="flex: 0 0 auto; padding: 10px 0px">
      <el-button @click="handleImport">导入</el-button>
    </div>
    <!-- 数据列表 -->
    <el-table
      v-loading="loading"
      size="small"
      :data="data"
      style="flex: 1 1 auto"
      height="100%"
      :header-cell-style="{
        'font-size': '12px',
        'font-weight': '400',
        color: '#777c94',
        background: 'var(--o-primary-bg-color)'
      }"
    >
      <el-table-column
        prop="supplierCorporationName"
        label="作业主体名称"
        width="200"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="fullName"
        label="姓名"
        width="120"
      ></el-table-column>
      <el-table-column
        prop="idNumber"
        label="身份证号"
        width="180"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="accumulatedIncome"
        label="累计收入"
        width="140"
      ></el-table-column>
      <el-table-column
        prop="accumulatedExpenses"
        label="累计费用"
        width="140"
      ></el-table-column>
      <el-table-column
        prop="accumulatedTaxFreeIncome"
        label="累计免税收入"
        width="150"
      ></el-table-column>
      <el-table-column
        prop="accumulatedDeductionExpenses"
        label="累计减除费用"
        width="150"
      ></el-table-column>
      <el-table-column
        prop="accumulatedOtherDeductions"
        label="累计依法确定的其他扣除"
        width="220"
      ></el-table-column>
      <el-table-column
        prop="accumulatedPrepaidTax"
        label="累计已预缴税额"
        width="150"
      ></el-table-column>
      <el-table-column
        prop="accumulatedTaxReductions"
        label="累计减免税额"
        width="150"
      ></el-table-column>
      <el-table-column
        prop="taxPeriod"
        label="税款所属期"
        width="140"
      ></el-table-column>
      <el-table-column
        prop="uploadTime"
        label="上传时间"
        width="180"
      ></el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-pagination
      @current-change="handleCurrentChange"
      :current-page="conditions.offset / conditions.limit + 1"
      :page-size="conditions.limit"
      layout="total, prev, pager, next"
      :total="total"
      style="flex: 0 0 auto; text-align: right; margin-top: 10px"
    ></el-pagination>

    <el-dialog
      title="导入"
      :visible.sync="importVisible"
      width="560px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="importFormRef"
        :model="importForm"
        :rules="rules"
        label-width="120px"
      >
        <el-form-item prop="supplierCorporationId" label="作业主体">
          <el-select
            filterable
            v-model="importForm.supplierCorporationId"
            placeholder="请选择作业主体"
            style="width: 280px"
            clearable
          >
            <el-option
              v-for="item in supplierOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="taxPeriod" label="税款所属期">
          <el-date-picker
            style="width: 280px"
            v-model="importForm.taxPeriod"
            format="yyyy-MM"
            value-format="yyyy-MM"
            type="month"
            placeholder="请选择税款所属期"
          >
          </el-date-picker>
        </el-form-item>
      </el-form>
      <ImportUpload
        taxCalculationMethod="PREVIOUS_INCOME_IMPORT"
        v-model="fileList"
        @getFileParams="getFileParams"
      />
      <p v-if="showErrInfo">
        您有<span style="color: red; margin: 0 5px">{{ failCount }}</span
        >条数据导入失败，<span
          style="color: green; cursor: pointer"
          @click="downloadErrFile"
          >点击下载错误文件</span
        >
      </p>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" :loading="loading" @click="handleConfirm"
          >确 定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ImportUpload from './components/importUpload.vue'
import { exportExcel } from 'kit/helpers/exportExcel'
import handleError from '../../../helpers/handleError'
import makeClient from '../../../services/operateLabor/makeClient'

const client = makeClient()

// 获取上一个月的 YYYY-MM 格式字符串
function getPreviousMonth() {
  const date = new Date()
  date.setMonth(date.getMonth() - 1)
  const year = date.getFullYear()
  const month = (date.getMonth() + 1).toString().padStart(2, '0')
  return `${year}-${month}`
}

export default {
  components: {
    ImportUpload
  },
  name: 'PreviousIncomeDeductions',
  data() {
    return {
      loading: true,
      data: [],
      total: 0,
      conditions: {
        offset: 0,
        limit: 10,
        withTotal: true,
        filters: {
          taxPeriod: '',
          fullName: ''
        }
      },
      // pickerOptions: {
      //   disabledDate(time) {
      //     // 只能选择当前月之前的月份
      //     const today = new Date()
      //     today.setHours(0, 0, 0, 0)
      //     // 设置为当月第一天
      //     const currentMonthFirstDay = new Date(
      //       today.getFullYear(),
      //       today.getMonth(),
      //       1
      //     )
      //     return time.getTime() >= currentMonthFirstDay.getTime()
      //   }
      // },
      importVisible: false,
      importForm: {
        supplierCorporationId: '',
        taxPeriod: ''
      },
      fileList: [],
      formData: null,
      rules: {
        supplierCorporationId: [
          { required: true, message: '请选择作业主体', trigger: 'change' }
        ],
        taxPeriod: [
          { required: true, message: '请选择税款所属期', trigger: 'change' }
        ]
      },
      showErrInfo: false,
      failCount: 0,
      uuid: '',
      supplierOptions: []
    }
  },
  async created() {
    await this.getList()
    await this.loadSupplierOptions()
  },
  methods: {
    async getList() {
      this.loading = true
      try {
        const [err, r] = await client.supplierSalaryListPreviousIncomeDeduction(
          { body: this.conditions }
        )
        if (err) {
          handleError(err)
          return
        }
        this.data = r.data.list || []
        this.total = r.data.total || 0
      } finally {
        this.loading = false
      }
    },
    async loadSupplierOptions() {
      try {
        const [err, response] = await client.listCorporation({
          body: { filters: {} }
        })

        if (err) {
          handleError(err)
          return
        }

        if (response && response.success && response.data) {
          this.supplierOptions = response.data.list || []
        }
      } catch (error) {
        console.error('加载作业主体选项失败：', error)
      }
    },
    onSearch() {
      this.conditions.offset = 0
      this.getList()
    },
    onReset() {
      this.conditions.filters = {
        taxPeriod: '',
        fullName: ''
      }
      this.onSearch()
    },
    handleCurrentChange(page) {
      this.conditions.offset = (page - 1) * this.conditions.limit
      this.getList()
    },
    handleImport() {
      this.importForm = {
        supplierCorporationId: '',
        taxPeriod: ''
      }
      this.fileList = []
      this.importVisible = true
      this.$nextTick(() => {
        this.$refs.importFormRef.clearValidate()
      })
    },
    cancel() {
      this.importVisible = false
    },
    getFileParams(params) {
      this.formData = new FormData()
      this.formData.set('file', params.file)
    },
    async handleConfirm() {
      await this.$refs.importFormRef.validate()
      if (!this.fileList.length) {
        this.$message.warning('请上传文件')
        return
      }
      this.loading = true
      this.formData.set(
        'supplierCorporationId',
        this.importForm.supplierCorporationId
      )
      this.formData.set('taxPeriod', this.importForm.taxPeriod)
      const [err, r] = await client.supplierSalaryPreviousIncomeImport({
        body: this.formData,
        requestInterceptor(resource, options) {
          delete options.headers['Content-Type']
          return [null, resource, options]
        }
      })
      this.loading = false
      if (err) {
        handleError(err)
        return
      }
      if (r.data.failCount) {
        this.failCount = r.data.failCount
        this.uuid = r.data.uuid
        this.showErrInfo = true
        return
      }
      this.importVisible = false
      this.$message.success('导入成功')
      this.onSearch()
    },
    async downloadErrFile() {
      const result = await client.supplierSalaryImportVerifyErrorLog(this.uuid)
      await exportExcel(result)
    }
  }
}
</script>
<style scoped>
.upload-template {
  position: absolute;
  bottom: 10px;
  left: 80px;
  z-index: 999;
  display: flex;
  justify-content: center;
  align-items: center;
}
::v-deep .el-upload-dragger {
  width: 500px;
  height: 230px;
  border: 1px dashed #d9d9d9;
}
</style>
